#! /user/bin/env python
#! -*- coding: utf-8 -*-
# File    :  connect.py
# Time    :  2025/04/17 14:20:49
# Author  :  lh
# Version :  1.0
# Description:


from typing import Dict, Any, Optional, Tuple, List
import asyncio
import torch
import uuid, base64, time
from datetime import datetime
from fastapi import WebSocket
# from memory_profiler import profile

from modules.logger import logger
from modules.feature import FeaturePipeline
from modules.decoder import ASRDecoder
from modules.lid_manager import LIDManager
from modules.error_codes import ErrorCode, ErrorResponse


def format_time(timestamp):
    return datetime.fromtimestamp(timestamp).strftime('%H:%M:%S.%f')[:-3]

class ConnectionManager:
    """
    连接管理类, 用于处理客户端连接、音频数据解析和管理解码。
    
    Attributes:
        active_connection (Dict[str, Any]): 存储活动连接的字典
        client_states (Dict[str, Dict[str, Any]]): 客户端状态信息
        configs (dict): 包含配置参数的字典
        feat_pipe (Any): 特征提取管道
        valid_sample_rate_list (List[int]): 支持的采样率列表
        expected_data_size (int): 预期音频数据大小
        packet_interval (float): 数据包间隔时间(秒)
        required_decoding_window (int): 解码所需的最小帧数
    """

    def __init__(self, args, configs, lid_manager=None, asr_manager=None):
        """
        每个ws连接维护独立数据和缓存队列

        Args:
            args: 服务器配置参数
            configs: 配置字典（用于单语种模式的默认配置）
            lid_manager: 语种识别管理器（可选，多语种模式时使用）
            asr_manager: ASR管理器（可选，多语种模式时使用；单语种模式时为None）
        """
        # ws连接
        self.active_connection: Dict[str: WebSocket] = {}
        # 客户端状态
        self.client_states: Dict[str: dict] = {}

        self.args = args
        self.configs = configs
        self.packet_interval = args.expected_time_interval
        self.expected_data_size = args.expected_data_size
        self.valid_sample_rate_list = args.valid_sample_rate_list
        self.lid_manager = lid_manager
        self.asr_manager = asr_manager  # ASR管理器，负责管理各语种的ASR模型

        # 根据模式初始化特征管道和词表
        self.is_multi_lang = asr_manager is not None
        if self.is_multi_lang:
            # 多语种模式：从ASR管理器动态获取
            self.feat_pipe = None  # 将在需要时动态获取
            self.symbol_table = None  # 将在需要时动态获取
            # 使用默认语种的配置来计算帧时长
            default_lang = args.default_language
            if asr_manager and asr_manager.is_language_loaded(default_lang):
                default_config = asr_manager.get_config(default_lang)
                self.frame_samples = default_config['feat_configs']['frame_length'] / 1000
            else:
                self.frame_samples = 25.0 / 1000  # 默认25ms
        else:
            # 单语种模式：使用传统方式（从全局变量获取）
            from modules.feature import FeaturePipeline
            self.feat_pipe = FeaturePipeline(configs['feat_configs'])
            self.symbol_table = None  # 将从全局变量获取
            # 一帧的时间长度
            self.frame_samples = self.feat_pipe.frame_length / 1000

        # 音频缓存清理配置
        self.max_cache_packets = getattr(args, 'max_cache_packets', 120)  # 默认120个数据包（60秒）
        self.cache_cleanup_interval = getattr(args, 'cache_cleanup_interval', 20)  # 每20个数据包检查一次

    def _get_feat_pipe(self, client_id: str):
        """
        获取特征管道，支持单语种和多语种模式

        Args:
            client_id: 客户端ID

        Returns:
            FeaturePipeline: 特征管道实例
        """
        if self.is_multi_lang:
            # 多语种模式：根据当前语种获取特征管道
            current_language = self.client_states[client_id].get("current_language")
            if current_language and self.asr_manager:
                # 使用当前语种的特征管道
                config = self.asr_manager.get_config(current_language)
                if config:
                    from modules.feature import FeaturePipeline
                    return FeaturePipeline(config['feat_configs'])

            # 如果没有当前语种，使用默认语种
            default_lang = self.args.default_language
            if self.asr_manager and self.asr_manager.is_language_loaded(default_lang):
                config = self.asr_manager.get_config(default_lang)
                if config:
                    from modules.feature import FeaturePipeline
                    return FeaturePipeline(config['feat_configs'])
        else:
            # 单语种模式：使用预初始化的特征管道
            if self.feat_pipe:
                return self.feat_pipe
            else:
                # 从全局变量获取
                from server import FEAT_PIPE
                return FEAT_PIPE

        # 如果都失败了，抛出异常
        raise RuntimeError(f"无法获取客户端 {client_id} 的特征管道")

    def _handle_language_options(self, client_id: str, json_data: dict):
        """
        处理客户端请求中的语种相关选项

        Args:
            client_id: 客户端ID
            json_data: 客户端请求数据
        """
        if not self.is_multi_lang:
            # 单语种模式下忽略语种相关选项
            return

        # 处理客户端指定的语种
        if "language" in json_data:
            specified_language = json_data["language"]
            if self.asr_manager and self.asr_manager.is_language_loaded(specified_language):
                # 客户端指定了支持的语种，直接使用该语种，跳过LID
                logger.info(f"client_id:{client_id} - 客户端指定语种: {specified_language}，跳过LID")
                self.client_states[client_id]["lid_enabled"] = False
                self.client_states[client_id]["detected_language"] = specified_language
                self.client_states[client_id]["language_confidence"] = 1.0
                self.client_states[client_id]["lid_confirmed"] = True

                # 立即切换到指定语种
                asyncio.create_task(self._switch_asr_model(client_id, specified_language))
            else:
                logger.warning(f"client_id:{client_id} - 客户端指定的语种 {specified_language} 不支持，将使用LID")

        # 处理LID启用选项（可选，默认启用）
        if "enable_lid" in json_data:
            # 只有在没有指定语种的情况下才允许禁用LID
            if "language" not in json_data or not self.asr_manager.is_language_loaded(json_data.get("language")):
                self.client_states[client_id]["lid_enabled"] = bool(json_data["enable_lid"])
                logger.info(f"client_id:{client_id} - LID功能: {'启用' if self.client_states[client_id]['lid_enabled'] else '禁用'}")

    async def connect(self, websocket: WebSocket, client_id: str):
        await websocket.accept()
        # 存储ws连接
        self.active_connection[client_id] = websocket
        # 初始化客户状态
        client_state = {
            "feat_buffer": [],   # 缓存的特征
            "frame_nums": 0,     # 累计的帧数量
            "packet_index": -1,   # 到达数据包的索引
            "sample_rate": None,
            "last_packet_time": None,     # 上一个数据包到达的时间 (用于检查实时率)
            "is_first": True,   # 是否为第一个数据包  (接受到第一个数据包时置为False, 用于后续验证数据包大小、采样率)
            "is_final": False,  # 是否为最后一个数据包
            "is_final_result": False,  # 是否为最后的识别结果
            "custom_separator": None,  # 用户自定义分隔符，将在第一个数据包中设置
            "decoder": None,    # 解码器将在收到第一个数据包时初始化
            "decoder_result" : "",
            "message_id_base": f"client_id:{client_id}_{uuid.uuid4()}",
            "message_counter": 0,
            # 缓存管理
            "packet_count": 0,  # 已处理的数据包计数
            "feat_buffer_timestamps": [],  # 特征缓存的时间戳
            "audio_buffer_timestamps": [],  # 音频缓存的时间戳
        }

        # 根据模式设置LID和语种相关状态
        if self.is_multi_lang:
            # 多语种模式：启用LID相关功能
            client_state.update({
                # LID相关状态
                "lid_enabled": True,  # 是否启用语种识别（可被客户端请求覆盖）
                "detected_language": None,  # 检测到的语种
                "language_confidence": 0.0,  # 语种识别置信度
                "audio_buffer": [],  # 用于LID的原始音频缓存
                "audio_duration": 0.0,  # 累计音频时长（秒）
                "lid_attempts": 0,  # LID尝试次数
                "lid_max_attempts": 6,  # 最大LID尝试次数（对应2.4秒）
                "lid_confirmed": False,  # LID结果是否已确认
                "vad_speech_detected": False,  # 是否检测到有效语音
                # 多语种对话支持
                "silence_frames": 0,  # 连续静音帧数
                "silence_threshold": 10,  # 静音阈值（帧数），超过此值认为是静音片段
                "last_speech_frame": -1,  # 最后一次检测到语音的帧索引
                "segment_count": 0,  # 语音片段计数
                # 多语种相关状态
                "current_language": None,  # 当前使用的语种
                "current_symbol_table": None,  # 当前使用的词表
                "current_config": None,  # 当前使用的配置
                "current_lang_config": None,  # 当前使用的语种配置
            })
        else:
            # 单语种模式：跳过LID，直接设置语种
            client_state.update({
                # LID相关状态（单语种模式下禁用）
                "lid_enabled": False,  # 单语种模式下禁用LID
                "detected_language": self.args.lang_code,  # 直接使用配置的语种
                "language_confidence": 1.0,  # 单语种模式下置信度为1.0
                "lid_confirmed": True,  # 单语种模式下直接确认
                # 单语种相关状态
                "current_language": self.args.lang_code,  # 当前语种就是配置的语种
                "current_symbol_table": None,  # 将从全局变量获取
                "current_config": self.configs,  # 使用传入的配置
                "current_lang_config": None,  # 单语种模式不需要
            })

        self.client_states[client_id] = client_state
    
    def _cleanup_old_cache(self, client_id: str):
        """
        清理旧的音频和特征缓存，防止内存无限增长
        Args:
            client_id: 客户端ID
        """
        state = self.client_states[client_id]
        current_time = time.time()

        # 清理特征缓存（保留最近的max_cache_packets个）
        if len(state["feat_buffer"]) > self.max_cache_packets:
            remove_count = len(state["feat_buffer"]) - self.max_cache_packets
            state["feat_buffer"] = state["feat_buffer"][remove_count:]
            state["feat_buffer_timestamps"] = state["feat_buffer_timestamps"][remove_count:]
            logger.debug(f"client_id:{client_id} - 清理了{remove_count}个旧的特征缓存")

        # 清理音频缓存（保留最近60秒的数据）
        if state["audio_buffer"] and state["audio_buffer_timestamps"]:
            cutoff_time = current_time - 60.0  # 保留60秒
            keep_indices = [i for i, ts in enumerate(state["audio_buffer_timestamps"]) if ts > cutoff_time]

            if len(keep_indices) < len(state["audio_buffer"]):
                remove_count = len(state["audio_buffer"]) - len(keep_indices)
                state["audio_buffer"] = [state["audio_buffer"][i] for i in keep_indices]
                state["audio_buffer_timestamps"] = [state["audio_buffer_timestamps"][i] for i in keep_indices]
                logger.debug(f"client_id:{client_id} - 清理了{remove_count}个旧的音频缓存")

    # @profile
    async def disconnect(self, client_id: str):
        try:
            logger.info(f"关闭 ws 连接")
            await self.active_connection[client_id].close()
        except:
            logger.info(f"ws 连接已经关闭了")
        # 移除ws连接
        if client_id in self.active_connection:
            del self.active_connection[client_id]
        # 移除客户状态, 并清空相关资源
        if client_id in self.client_states:
            # 清空缓存
            self.client_states[client_id]["feat_buffer"].clear()
            self.client_states[client_id]["feat_buffer_timestamps"].clear()
            # 清空LID音频缓存
            if "audio_buffer" in self.client_states[client_id]:
                self.client_states[client_id]["audio_buffer"].clear()
                self.client_states[client_id]["audio_buffer_timestamps"].clear()
            # 清除ASRDecoder实例
            if "decoder" in self.client_states[client_id]:
                del self.client_states[client_id]["decoder"]
            # 移除客户状态
            del self.client_states[client_id]

    def generate_message_id(self, client_id: str) -> str:
        self.client_states[client_id]["message_counter"] += 1
        base = self.client_states[client_id]["message_id_base"]
        index = self.client_states[client_id]["message_counter"]
        return f"{base}_{index}"

    async def on_error(self, error_code: ErrorCode, client_id: str, **kwargs):
        """
        处理错误并发送错误信息给客户端
        Args:
            error_code (ErrorCode): 错误码枚举
            client_id (str): 客户端唯一标识
            **kwargs: 错误消息格式化参数
        Returns:
            None
        """
        # 更新错误统计
        from modules.monitoring import get_global_monitor
        monitor = get_global_monitor()
        if monitor:
            monitor.increment_error_count()

        if client_id in self.active_connection and self.active_connection[client_id]:
            index = self.client_states[client_id]["message_counter"]
            message_id = self.generate_message_id(client_id)

            error_response = ErrorResponse.create_error_response(
                error_code=error_code,
                client_id=client_id,
                index=index,
                message_id=message_id,
                **kwargs
            )

            logger.warning(f"client_id:{client_id} - <<< [发送] 错误信息: {error_response}")
            await self.active_connection[client_id].send_json(error_response)
            logger.info(f"client_id: {client_id} - 关闭连接，清理资源")
            await self.disconnect(client_id)

    async def on_error_legacy(self, code: int, message: str, client_id: str):
        """
        兼容旧版本的错误处理方法（逐步废弃）
        """
        # 尝试映射到新的错误码
        error_code = ErrorCode(code) if code in [e.value for e in ErrorCode] else ErrorCode.INTERNAL_ERROR
        await self.on_error(error_code, client_id, details=message)

    async def on_result(self, result: str, client_id: str):
        """
        发送识别结果给客户端。
        Args:
            client_id (str): 客户端唯一标识
            result (str): 当前识别结果
        Returns:
            None
        """
        if self.active_connection[client_id]:
            index = self.client_states[client_id]["message_counter"]
            final = self.client_states[client_id]['is_final_result']
            response = {
                    "code": 200,
                    "state": "success",
                    "index": index,
                    "result": result,
                    "voice_id": client_id,
                    "message_id": self.generate_message_id(client_id),
                    "final": 1 if final else 0
            }

            # 添加语种识别结果（如果可用且已确认）
            if (self.client_states[client_id].get("lid_confirmed", False) and
                self.client_states[client_id].get("detected_language")):
                response["language"] = self.client_states[client_id]["detected_language"]
                response["language_confidence"] = self.client_states[client_id].get("language_confidence", 0.0)

            logger.info(f"client_id:{client_id} - <<< [发送] 第{index}个数据包, 更新识别结果: \"{result}\"")
            await self.active_connection[client_id].send_json(response)

    async def on_check(self, client_id: str, json_data: Dict[str, Any]) -> bool:
        """
        检查数据包合法性, 解析音频数据并获取音频特征。
        Args:
            client_id (str): 客户端唯一标识
            json_data (Dict[str, Any]): 接收到的 JSON 数据
        Returns:
            bool: 检查结果(True 表示合法, False 表示非法)
        """
        if client_id not in self.active_connection:
            logger.error(f"客户已断开连接 \"{client_id}\"")
            raise KeyError(f"client disconnect: {client_id}")

        # 1. 检查必要字段
        # "index", 发送数据包的序号, 从0开始
        # "audio_data", Base64编码的音频数据, 数据长度需要满足固定要求
        # "sample_rate", 推荐16000
        # "is_final", True 代表最后一个数据包
        # "custom_separator", 可选的自定义分隔符参数
        required_keys = ["index", "audio_data", "sample_rate", "is_final"]
        for key in required_keys:
            if key not in json_data:
                await self.on_error(ErrorCode.MISSING_REQUIRED_FIELD, client_id, field=key)
                return False

        # 处理可选参数（仅在第一个数据包中处理）
        if json_data["index"] == 0:
            # 处理自定义分隔符参数
            if "custom_separator" in json_data:
                self.client_states[client_id]["custom_separator"] = json_data["custom_separator"]
                logger.info(f"client_id:{client_id} - 设置自定义分隔符: \"{json_data['custom_separator']}\"")

            # 处理语种相关选项
            self._handle_language_options(client_id, json_data)

        # 延迟初始化解码器，等待LID结果确定语种后再初始化
        # 如果LID功能未启用或不可用，则立即初始化解码器
        should_init_decoder = (
            self.client_states[client_id]["decoder"] is None and
            (not self.client_states[client_id]["lid_enabled"] or
             not self.lid_manager or
             not self.lid_manager.is_available() or
             self.client_states[client_id]["lid_confirmed"])
        )

        if should_init_decoder:
            custom_separator = self.client_states[client_id]["custom_separator"]

            # 多语种模式下使用动态切换的配置
            if self.asr_manager and self.client_states[client_id]["current_lang_config"]:
                # 使用切换后的语种配置
                decoder_configs = self.client_states[client_id]["current_config"]
                decoder_symbol_table = self.client_states[client_id]["current_symbol_table"]
                logger.info(f"client_id:{client_id} - 使用语种 {self.client_states[client_id]['current_language']} 初始化解码器")
            else:
                # 单语种模式或多语种模式下的默认配置
                decoder_configs = self.configs
                if self.is_multi_lang:
                    # 多语种模式下使用默认语种的词表
                    default_lang = self.args.default_language
                    decoder_symbol_table = self.asr_manager.get_symbol_table(default_lang)
                else:
                    # 单语种模式下从全局变量获取词表
                    from server import SYMBOL_TABLE
                    decoder_symbol_table = SYMBOL_TABLE
                logger.info(f"client_id:{client_id} - 使用默认配置初始化解码器")

            self.client_states[client_id]["decoder"] = ASRDecoder(
                self.args, decoder_configs, decoder_symbol_table, custom_separator
            )
            self.required_decoding_window = self.client_states[client_id]["decoder"].decoding_window
            logger.info(f"client_id:{client_id} - 解码器初始化完成，分隔符: \"{self.client_states[client_id]['decoder'].blank}\"")


        # 2. 检查数据包索引, index 从0开始, 保证顺序到达
        index = json_data["index"]
        expected_index = self.client_states[client_id]['packet_index'] + 1
        if index != expected_index:
            await self.on_error(
                ErrorCode.INVALID_PACKET_INDEX,
                client_id,
                expected=expected_index,
                actual=index
            )
            return False
        # 更新索引状态
        self.client_states[client_id]['packet_index'] = index
        self.client_states[client_id]["packet_count"] += 1

        # 定期清理旧缓存
        if self.client_states[client_id]["packet_count"] % self.cache_cleanup_interval == 0:
            self._cleanup_old_cache(client_id)

        # 3. 是否为最后一个数据包
        is_final = json_data['is_final']
        if is_final:
            self.client_states[client_id]['is_final'] = True

        # 4. 检查数据包发送的实时率
        current_time = time.time()
        last_packet_time = self.client_states[client_id].get('last_packet_time', None)
        if last_packet_time is None:   # 第一个数据包
            self.client_states[client_id]['is_first'] = True
        else:
            self.client_states[client_id]['is_first'] = False
            if current_time - last_packet_time > self.packet_interval:
                current_time_fmt = format_time(current_time)
                last_time_fmt = format_time(last_packet_time)
                await self.on_error(
                    ErrorCode.PACKET_TIMEOUT,
                    client_id,
                    timeout=self.packet_interval,
                    last_time=last_time_fmt,
                    current_time=current_time_fmt
                )
                return False
        # 更新数据包到达时间
        self.client_states[client_id]['last_packet_time'] = current_time  

        # 5. 检查采样率一致性
        sample_rate = json_data['sample_rate']
        last_sample_rate = self.client_states[client_id].get('sample_rate', None)
        
        if last_sample_rate is not None and sample_rate != last_sample_rate:
            await self.on_error(
                ErrorCode.INVALID_SAMPLE_RATE,
                client_id,
                last_rate=last_sample_rate,
                current_rate=sample_rate
            )
            return False

        if sample_rate not in self.valid_sample_rate_list:
            await self.on_error(
                ErrorCode.INVALID_SAMPLE_RATE,
                client_id,
                supported_rates=self.valid_sample_rate_list
            )
            return False
    
        # 6.1 解码Base64编码的音频数据
        try:
            audio_data_base64 = json_data["audio_data"]
            pcm_bytes = base64.b64decode(audio_data_base64)
        except Exception as e:
            await self.on_error(
                ErrorCode.INVALID_AUDIO_FORMAT,
                client_id,
                details=f"Base64解码失败: {str(e)}"
            )
            return False
        # 6.2 检查pcm音频数据流大小
        if is_final:
            if len(pcm_bytes) > self.expected_data_size:
                await self.on_error(
                    ErrorCode.INVALID_DATA_SIZE,
                    client_id,
                    expected=self.expected_data_size,
                    actual=len(pcm_bytes)
                )
                return False
        else:
            if len(pcm_bytes) != self.expected_data_size:
                await self.on_error(
                    ErrorCode.INVALID_DATA_SIZE,
                    client_id,
                    expected=self.expected_data_size,
                    actual=len(pcm_bytes)
                )
                return False

        # 6.3 提取特征并处理LID逻辑
        try:
            # 获取特征管道
            feat_pipe = self._get_feat_pipe(client_id)
            waveform = feat_pipe.to_waveform(pcm_bytes)  # torch.Tensor
            if is_final and waveform.size(0) < self.frame_samples * sample_rate:
                # 最后一个数据包达不到最小数据要求（小于一个帧长）则跳过
                return True

            # 6.3.1 静音检测和LID处理逻辑
            await self._process_silence_and_lid(client_id, waveform, sample_rate, is_final)

            # 6.3.2 只有在LID确认后或LID未启用时才提取ASR特征
            if (not self.client_states[client_id]["lid_enabled"] or
                self.client_states[client_id]["lid_confirmed"] or
                not self.lid_manager or
                not self.lid_manager.is_available()):

                feat = feat_pipe.feat_func(waveform, sample_rate)   # torch.Tensor
                current_time = time.time()
                self.client_states[client_id]['feat_buffer'].append(feat)
                self.client_states[client_id]['feat_buffer_timestamps'].append(current_time)
                # logger.debug(f"client_id:{client_id} - >>> waveform shape: {waveform.shape}")
                # logger.debug(f"client_id:{client_id} - >>> feat shape: {feat.shape}")

                # 6.4 更新帧数
                cur_frames = feat.shape[0]
                self.client_states[client_id]['frame_nums'] += cur_frames
                logger.info(f"client_id:{client_id} - >>> [解析] 第{index}个数据包, 累计帧数: {self.client_states[client_id]['frame_nums']}")
            else:
                logger.debug(f"client_id:{client_id} - >>> [等待LID] 第{index}个数据包, 等待语种识别确认")

            return True
        except Exception as e:
            await self.on_error(code=4009, message=f"Feature extraction error: {str(e)}", client_id=client_id)
            return False

    async def _process_silence_and_lid(self, client_id: str, waveform: torch.Tensor, sample_rate: int, is_final: bool):
        """
        处理静音检测和语种识别逻辑，支持多语种对话场景
        Args:
            client_id: 客户端ID
            waveform: 音频波形
            sample_rate: 采样率
            is_final: 是否为最后一个数据包
        """
        if not self.lid_manager or not self.lid_manager.is_available():
            return

        if not self.client_states[client_id]["lid_enabled"]:
            return

        # 检测当前帧是否包含语音
        current_has_speech = self.lid_manager.detect_speech(waveform, sample_rate)
        current_frame = self.client_states[client_id]['packet_index']

        if current_has_speech:
            # 检测到语音
            self.client_states[client_id]["silence_frames"] = 0

            # 如果这是静音后的第一个语音帧，重置LID状态
            # 使用静音帧数而不是帧间隔来判断
            if (self.client_states[client_id]["lid_confirmed"] and
                self.client_states[client_id]["silence_frames"] >= self.client_states[client_id]["silence_threshold"]):
                logger.info(f"client_id:{client_id} - 检测到静音后的新语音片段（静音帧数：{self.client_states[client_id]['silence_frames']}），重置LID状态")
                await self._reset_lid_state(client_id)

            self.client_states[client_id]["last_speech_frame"] = current_frame
        else:
            # 检测到静音
            self.client_states[client_id]["silence_frames"] += 1

        # 如果LID已确认且当前是静音，不需要进行LID处理
        if self.client_states[client_id]["lid_confirmed"] and not current_has_speech:
            return

        # 缓存音频数据（只在有语音或LID未确认时缓存）
        if current_has_speech or not self.client_states[client_id]["lid_confirmed"]:
            current_time = time.time()
            self.client_states[client_id]["audio_buffer"].append(waveform)
            self.client_states[client_id]["audio_buffer_timestamps"].append(current_time)
            audio_duration = waveform.shape[0] / sample_rate
            self.client_states[client_id]["audio_duration"] += audio_duration

        # 检查是否需要进行LID
        should_perform_lid = False

        # 第一次检测：0.4秒后检查是否有有效语音
        if (self.client_states[client_id]["lid_attempts"] == 0 and
            self.client_states[client_id]["audio_duration"] >= 0.4):

            # 拼接所有音频
            combined_audio = torch.cat(self.client_states[client_id]["audio_buffer"], dim=0)

            # VAD检测
            has_speech = self.lid_manager.detect_speech(combined_audio, sample_rate)
            self.client_states[client_id]["vad_speech_detected"] = has_speech

            if has_speech:
                should_perform_lid = True
                logger.info(f"client_id:{client_id} - 片段{self.client_states[client_id]['segment_count']} 检测到有效语音，开始语种识别")
            else:
                logger.debug(f"client_id:{client_id} - 未检测到有效语音，继续等待")

        # 后续检测：每0.4秒进行一次，最多6次（2.4秒）
        elif (self.client_states[client_id]["vad_speech_detected"] and
              self.client_states[client_id]["lid_attempts"] < self.client_states[client_id]["lid_max_attempts"] and
              (self.client_states[client_id]["audio_duration"] >=
               (self.client_states[client_id]["lid_attempts"] + 1) * 0.4)):
            should_perform_lid = True

        # 最后一个数据包时强制进行LID（如果还未确认）
        elif is_final and self.client_states[client_id]["vad_speech_detected"]:
            should_perform_lid = True

        if should_perform_lid:
            await self._perform_lid(client_id, sample_rate)

    async def _reset_lid_state(self, client_id: str):
        """
        重置LID状态，用于新的语音片段
        Args:
            client_id: 客户端ID
        """
        logger.info(f"client_id:{client_id} - 重置LID状态，开始新的语音片段识别")

        # 重置LID相关状态
        self.client_states[client_id]["detected_language"] = None
        self.client_states[client_id]["language_confidence"] = 0.0
        self.client_states[client_id]["audio_buffer"] = []
        self.client_states[client_id]["audio_buffer_timestamps"] = []
        self.client_states[client_id]["audio_duration"] = 0.0
        self.client_states[client_id]["lid_attempts"] = 0
        self.client_states[client_id]["lid_confirmed"] = False
        self.client_states[client_id]["vad_speech_detected"] = False
        self.client_states[client_id]["segment_count"] += 1

        # 重置解码器，因为可能需要切换到新的语种
        if self.client_states[client_id]["decoder"] is not None:
            logger.info(f"client_id:{client_id} - 重置解码器以准备语种切换")
            self.client_states[client_id]["decoder"] = None

    async def _perform_lid(self, client_id: str, sample_rate: int):
        """
        执行语种识别
        Args:
            client_id: 客户端ID
            sample_rate: 采样率
        """
        try:
            # 拼接所有音频
            combined_audio = torch.cat(self.client_states[client_id]["audio_buffer"], dim=0)

            # 执行LID
            lid_result = self.lid_manager.predict_language(combined_audio, sample_rate)

            self.client_states[client_id]["lid_attempts"] += 1
            current_language = lid_result.get("predict", "unknown")
            current_confidence = lid_result.get("scores", {}).get(current_language, 0.0)

            logger.info(f"client_id:{client_id} - LID尝试 {self.client_states[client_id]['lid_attempts']}: "
                       f"语种={current_language}, 置信度={current_confidence}")

            # 更新检测结果
            self.client_states[client_id]["detected_language"] = current_language
            self.client_states[client_id]["language_confidence"] = current_confidence

            # 判断是否确认结果
            should_confirm = (
                self.client_states[client_id]["lid_attempts"] >= self.client_states[client_id]["lid_max_attempts"] or
                current_confidence >= 0.8 or  # 高置信度
                self.client_states[client_id]["audio_duration"] >= 2.4  # 达到最大时长
            )

            if should_confirm:
                self.client_states[client_id]["lid_confirmed"] = True
                logger.info(f"client_id:{client_id} - LID结果确认: 语种={current_language}, 置信度={current_confidence}")

                # 根据识别的语种切换ASR模型（多语种模式）
                if self.asr_manager and current_language != "unknown":
                    success = await self._switch_asr_model(client_id, current_language)
                    if not success:
                        logger.warning(f"client_id:{client_id} - 切换到语种 {current_language} 失败，使用默认模型")
                        # 使用默认语种
                        default_lang = self.args.default_language
                        await self._switch_asr_model(client_id, default_lang)

        except Exception as e:
            logger.error(f"client_id:{client_id} - LID执行失败: {e}")
            # LID失败时，确认为未知语种，继续使用默认ASR
            self.client_states[client_id]["detected_language"] = "unknown"
            self.client_states[client_id]["lid_confirmed"] = True

    async def _switch_asr_model(self, client_id: str, target_language: str) -> bool:
        """
        切换到指定语种的ASR模型

        Args:
            client_id: 客户端ID
            target_language: 目标语种代码

        Returns:
            bool: 是否切换成功
        """
        try:
            if not self.asr_manager:
                logger.warning(f"client_id:{client_id} - ASR管理器未初始化，无法切换模型")
                return False

            # 切换全局ONNX模型会话
            if not self.asr_manager.switch_to_language(target_language):
                logger.error(f"client_id:{client_id} - 切换到语种 {target_language} 失败")
                return False

            # 获取目标语种的配置和词表
            target_lang_config = self.asr_manager.get_lang_config(target_language)
            target_symbol_table = self.asr_manager.get_symbol_table(target_language)
            target_config = self.asr_manager.get_config(target_language)

            if not target_lang_config or not target_symbol_table or not target_config:
                logger.error(f"client_id:{client_id} - 获取语种 {target_language} 的配置失败")
                return False

            # 更新客户端状态中的语种信息
            self.client_states[client_id]["current_language"] = target_language
            self.client_states[client_id]["current_symbol_table"] = target_symbol_table
            self.client_states[client_id]["current_config"] = target_config
            self.client_states[client_id]["current_lang_config"] = target_lang_config

            # 重置解码器以使用新的语种配置
            if self.client_states[client_id]["decoder"] is not None:
                logger.info(f"client_id:{client_id} - 重置解码器以使用新语种配置")
                self.client_states[client_id]["decoder"] = None

            logger.info(f"client_id:{client_id} - 成功切换到语种: {target_language}")
            return True

        except Exception as e:
            logger.error(f"client_id:{client_id} - 切换ASR模型到 {target_language} 时发生异常: {e}")
            return False

    async def on_decode(self, client_id: str) -> Tuple[bool, Optional[str]]:
        """
        当帧数满足解码所需的最小帧数时, 开始逐 chunk 解码。
        Args:
            client_id (str): 客户端唯一标识
        Returns:
            Tuple[bool, Optional[str]]: 是否成功解码(True/False), 解码结果或空字符串
        """
        try:
            # 检查是否需要等待LID确认
            if (self.client_states[client_id]["lid_enabled"] and
                self.lid_manager and
                self.lid_manager.is_available() and
                not self.client_states[client_id]["lid_confirmed"]):

                # 如果是最后一个数据包但LID还未确认，强制确认
                if self.client_states[client_id]['is_final']:
                    self.client_states[client_id]["lid_confirmed"] = True
                    if not self.client_states[client_id]["detected_language"]:
                        self.client_states[client_id]["detected_language"] = "unknown"
                    logger.info(f"client_id:{client_id} - 最后数据包，强制确认LID结果")
                else:
                    # 等待LID确认
                    logger.debug(f"client_id:{client_id} - 等待LID确认，暂停解码")
                    await asyncio.sleep(0.01)
                    return (True, "")

            # 确保解码器已初始化
            if self.client_states[client_id]["decoder"] is None:
                custom_separator = self.client_states[client_id]["custom_separator"]

                # 多语种模式下使用动态切换的配置
                if self.asr_manager and self.client_states[client_id]["current_lang_config"]:
                    # 使用切换后的语种配置
                    decoder_configs = self.client_states[client_id]["current_config"]
                    decoder_symbol_table = self.client_states[client_id]["current_symbol_table"]
                    logger.info(f"client_id:{client_id} - 延迟初始化解码器，使用语种: {self.client_states[client_id]['current_language']}")
                else:
                    # 单语种模式或多语种模式下的默认配置
                    decoder_configs = self.configs
                    decoder_symbol_table = self.symbol_table
                    logger.info(f"client_id:{client_id} - 延迟初始化解码器，使用默认配置")

                self.client_states[client_id]["decoder"] = ASRDecoder(
                    self.args, decoder_configs, decoder_symbol_table, custom_separator
                )
                self.required_decoding_window = self.client_states[client_id]["decoder"].decoding_window
                logger.info(f"client_id:{client_id} - 延迟初始化解码器完成")

            while True:
                frame_nums = self.client_states[client_id]['frame_nums']

                if frame_nums >= self.required_decoding_window:
                    logger.debug(f"client_id:{client_id} - 所需帧数: {self.required_decoding_window}, 目前帧数: {frame_nums}, 开始解码")

                    # 拼接特征并添加 batch 维度
                    accum_feats = torch.cat(self.client_states[client_id]['feat_buffer'], dim=0).unsqueeze(0)

                    is_final = self.client_states[client_id]['is_final']
                    asr_model = self.client_states[client_id]['decoder']
                    asr_model.decode(accum_feats, client_id, is_final) # 累积的解码结果存储在 asr_model.result 中
                    last_result = self.client_states[client_id]['decoder_result']
                    cur_result = asr_model.result
                    if is_final:
                        logger.info(f"client_id:{client_id} - *** 最后一个数据包完成解码 ***")
                        self.client_states[client_id]['is_final_result'] = True
                        return (True, cur_result)    # 最后一个数据包，无论识别结果变不变，都要发送响应数据包

                    if cur_result != last_result:
                        logger.debug(f"client_id:{client_id} - chunk 完成解码, 更新识别结果")
                        self.client_states[client_id]['decoder_result'] = cur_result
                        return (True, cur_result)
                    else:
                        logger.debug(f"client_id:{client_id} - chunk 完成解码, 识别结果不变")
                        return (True, "")
                else:
                    logger.debug(f"client_id:{client_id} - 所需帧数: {self.required_decoding_window}, 目前帧数: {frame_nums}, 继续等待数据包")
                    await asyncio.sleep(0.01)   # 低频轮询避免忙等待 (10ms 间隔)
                    return (True, "")

        except Exception as e:
            await self.on_error(code=5001, message=f"Decode error", client_id=client_id)
            return (False, None)
